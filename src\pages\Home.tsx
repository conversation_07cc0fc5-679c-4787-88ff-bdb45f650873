import React, { useState, useEffect, useRef, Suspense, lazy } from 'react';
import { useTranslation } from 'react-i18next';
import { useUser } from '../contexts/UserContext';
import { useTheme } from '../contexts/ThemeContext';
import { motion } from 'framer-motion';
import LoginPrompt from '../components/LoginPrompt';
import { getCurrentUser, checkRavenUsed } from '../services/userService';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import VipPromptDialog from '../components/VipPromptDialog';
import SEO from '../components/SEO';
import { getFontClass } from '../utils/fontUtils';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { scrollToTop } from '../utils/scrollHelper';
import {
  getReaderVotes,
  getUserVotedReaders,
  toggleReaderVoteLocal,
  submitPendingVotes,
  getPendingVotesCount
} from '../services/readerService';
import { toast } from 'react-hot-toast';
import { formatNumber } from '../utils/numberUtils';
import { fetchParagraphAudio } from '../components/speech/audioUtils';
import { getBrowserFingerprint } from '../utils/fingerprint';
import { checkAnonymousEligibility } from '../services/anonymousService';

// 延迟加载SpotlightCard组件
const SpotlightCard = lazy(
  () => import("../blocks/Components/SpotlightCard/SpotlightCard")
);

// 导入CdnLazyImage组件
import { CdnLazyImage } from '../components/CdnImageExport';

// 导入剩余次数显示组件
import RemainingReadsDisplay from '../components/RemainingReadsDisplay';

interface Reader {
  id: string;
  nameEn: string;
  price: 'free' | 'paid';
  isFreeFirstTime?: boolean; // 添加标识是否为首次免费使用
  voteCount?: number; // 添加投票数字段
  hasVoted?: boolean; // 添加用户是否已投票字段
}

// 定义价格标签的接口
interface PriceLabel {
  text: string;
  className: string;
  style?: React.CSSProperties;
}

const defaultReaders: Reader[] = [
  {
    id: 'basic',
    nameEn: 'Molly',
    price: 'free',
    voteCount: 1500  // 茉伊排第一，点赞数最高
  },
  {
    id: 'elias',
    nameEn: 'Elias',
    price: 'paid',
    voteCount: 132  // 林曜排第五
  },
  {
    id: 'claire',
    nameEn: 'Claire',
    price: 'paid',
    voteCount: 251  // 苏谨排第四
  },
  {
    id: 'raven',
    nameEn: 'Raven',
    price: 'paid',
    voteCount: 743  // 渡鸦排第二
  },
  {
    id: 'aurora',
    nameEn: 'Aurora',
    price: 'paid',
    voteCount: 86   // 月熙排第六，点赞数最低
  },
  {
    id: 'vincent',
    nameEn: 'Vincent',
    price: 'paid',
    voteCount: 582  // 文森特排第三
  }
];

const Home: React.FC = () => {
  const { navigate } = useLanguageNavigate();
  const { user, setUser } = useUser();
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [showVipPrompt, setShowVipPrompt] = useState(false);
  const [, setHasUsedRaven] = useState(true); // 默认为已使用过，变量未使用但保留setter函数
  const [readers, setReaders] = useState<Reader[]>(defaultReaders);
  const [playingReaderId, setPlayingReaderId] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<{[key: string]: HTMLAudioElement}>({});
  const [isVoting, setIsVoting] = useState<{[key: string]: boolean}>({});
  const [loadingAudio, setLoadingAudio] = useState<{[key: string]: boolean}>({});
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    mindset: false,
    question_skills: false,
    usage_guide: false,
    result_cognition: false,
    environment: false,
    special_notes: false,
    learning_tips: false
  });

  // 获取当前语言
  const currentLanguage = i18n.language;
  // 允许在所有语言环境下显示试听按钮
  const showPreviewButton = true; // 移除语言限制，在所有语言下都显示试听按钮

  // 试听示例文本 - 根据语言选择不同的示例文本
  const getSampleText = () => {
    // 获取语言代码 (zh, en, ja)
    const langCode = currentLanguage.split('-')[0];

    switch(langCode) {
      case 'en':
        return "Welcome to the world of Tarot. Let me guide you through the mysteries of destiny.";
      case 'ja':
        return "タロットの世界へようこそ。運命の神秘をご案内します。";
      case 'zh':
      default:
        return "欢迎来到塔罗世界，让我为您解读命运的奥秘。";
    }
  };

  const sampleText = getSampleText();

  // 添加五步骤轮播所需的状态
  const [selectedStep, setSelectedStep] = useState('step1');
  const stepRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [stepCursorPosition, setStepCursorPosition] = useState({ top: 0, height: 0 });

  // 更新光标位置的函数
  const updateStepCursorPosition = () => {
    const stepIndex = selectedStep === 'step1' ? 0 : 
                   selectedStep === 'step2' ? 1 : 
                   selectedStep === 'step3' ? 2 :
                   selectedStep === 'step4' ? 3 : 4;
    
    const currentOption = stepRefs.current[stepIndex];
    if (currentOption) {
      const { offsetTop, clientHeight } = currentOption;
      setStepCursorPosition({
        top: offsetTop + 20, // 稍微向下偏移，使其更居中
        height: clientHeight - 40 // 稍微缩短高度
      });
    }
  };
  
  useEffect(() => {
    // 检查用户是否使用过渡鸦
    const checkRavenUsage = async () => {
      try {
        // 只有在用户已登录的情况下才检查
        let ravenUsed = true; // 默认为已使用
        if (user) {
          ravenUsed = await checkRavenUsed();
        }
        setHasUsedRaven(ravenUsed);

        // 查找渡鸦和基础占卜师
        const basicReader = defaultReaders.find(r => r.id === 'basic');
        const ravenReader = defaultReaders.find(r => r.id === 'raven');
        const otherReaders = defaultReaders.filter(r => r.id !== 'basic' && r.id !== 'raven');

        // 确保基础占卜师始终存在
        let updatedReaders: Reader[] = [];

        // 根据是否使用过渡鸦来排序和修改readers数组
        if (!ravenUsed) {
          // 对于新用户未使用过渡鸦的情况
          if (basicReader && ravenReader) {
            // 确保基础占卜师(Molly)显示在首位
            updatedReaders = [
              basicReader, // 基础占卜师放在首位
              { ...ravenReader, isFreeFirstTime: true, price: 'free' as const }, // 将渡鸦标记为免费，首次使用
              ...otherReaders
            ];
          } else if (basicReader) {
            updatedReaders = [
              basicReader, // 只有基础占卜师
              ...otherReaders
            ];
          } else if (ravenReader) {
            // 如果没有基础占卜师但有渡鸦
            updatedReaders = [
              { ...ravenReader, isFreeFirstTime: true, price: 'free' as const },
              ...otherReaders
            ];
          } else {
            updatedReaders = [...defaultReaders]; // 使用默认列表
          }
        } else {
          // 已使用过渡鸦的情况
          if (basicReader && ravenReader) {
            updatedReaders = [
              basicReader, // 基础占卜师放在首位
              ravenReader,
              ...otherReaders
            ];
          } else if (basicReader) {
            updatedReaders = [
              basicReader,
              ...otherReaders
            ];
          } else {
            updatedReaders = [...defaultReaders]; // 使用默认列表
          }
        }

        // 获取用户已点赞的所有占卜师
        let userVotedReaderIds: string[] = [];
        if (user) {
          try {
            userVotedReaderIds = await getUserVotedReaders();
          } catch (error) {
            // console.error('Error fetching user voted readers:', error);
          }
        }

        // 获取每个占卜师的投票数和用户投票状态
        const readersWithVotes = await Promise.all(
          updatedReaders.map(async (reader) => {
            let voteCount;
            try {
              voteCount = await getReaderVotes(reader.id);
            } catch (error) {
              // console.error(`获取占卜师 ${reader.id} 点赞数失败:`, error);
              // 获取失败时使用默认点赞数
              voteCount = reader.voteCount || 0;
            }
            // 根据获取的用户已点赞列表，直接设置hasVoted状态
            const hasVoted = user ? userVotedReaderIds.includes(reader.id) : false;
            return {
              ...reader,
              voteCount,
              hasVoted
            };
          })
        );

        setReaders(readersWithVotes);
      } catch (error) {
        // console.error('Error checking raven usage or reader votes:', error);
        // 出错时，确保至少显示基础占卜师
        const basicReader = defaultReaders.find(r => r.id === 'basic');
        if (basicReader) {
          setReaders([basicReader]);
        } else {
          const basicReaders = defaultReaders.filter(r => r.price === 'free');
          setReaders(basicReaders);
        }
      }
    };

    checkRavenUsage();

    return () => {
      // 清理所有音频资源
      Object.values(audioElements).forEach(audio => {
        audio.pause();
        audio.src = '';
      });

      // 在组件卸载时，自动提交所有待处理的点赞记录
      const submitVotes = async () => {
        try {
          // 检查是否有待处理的点赞记录
          const pendingCount = getPendingVotesCount();
          if (pendingCount > 0 && user) {
            await submitPendingVotes();
          }
        } catch (error) {
        }
      };

      // 执行提交操作
      submitVotes();
    };
  }, [audioElements, user]);

  // 当步骤选择改变或组件挂载时更新光标位置
  useEffect(() => {
    updateStepCursorPosition();

    // 添加窗口大小变化的监听，确保响应式布局下光标位置正确
    window.addEventListener('resize', updateStepCursorPosition);
    return () => window.removeEventListener('resize', updateStepCursorPosition);
  }, [selectedStep]);
  
  // 添加自动切换功能 - 仅在桌面端
  useEffect(() => {
    // 检查是否为移动端
    const isMobile = window.innerWidth < 768; // md断点通常是768px
    
    // 如果是移动端，不启用自动切换
    if (isMobile) return;
    
    const autoChangeInterval = setInterval(() => {
      // 循环切换五个选项
      setSelectedStep(prevStep => {
        if (prevStep === 'step1') return 'step2';
        if (prevStep === 'step2') return 'step3';
        if (prevStep === 'step3') return 'step4';
        if (prevStep === 'step4') return 'step5';
        return 'step1';
      });
    }, 5000); // 每5秒切换一次
    
    return () => clearInterval(autoChangeInterval); // 清理定时器
  }, []);

  // 统一的权限检查函数
  const checkUserPermission = async () => {
    if (!user) {
      // 匿名用户需要检查是否已使用过免费机会
      try {
        const fingerprint = await getBrowserFingerprint();
        const eligibility = await checkAnonymousEligibility(fingerprint);
        if (!eligibility.canUse) {
          setShowLoginPrompt(true);
          return false;
        }
        return true;
      } catch (error) {
        console.error('检查匿名用户权限失败:', error);
        setShowLoginPrompt(true);
        return false;
      }
    }
    if (user.vipStatus === 'active') return true;
    if (user.remainingReads <= 0) {
      setShowVipPrompt(true);
      return false;
    }
    return true;
  };

  const handleReaderSelect = async (reader: Reader) => {
    // 先检查基本权限
    const hasPermission = await checkUserPermission();
    if (!hasPermission) {
      return;
    }

    // 匿名用户只能选择基础占卜师
    if (!user && reader.price === 'paid') {
      setShowLoginPrompt(true);
      return;
    }

    // 如果不是首次免费的渡鸦，并且是付费塔罗师，并且用户不是VIP，显示VIP提示弹窗
    if (reader.price === 'paid' && !reader.isFreeFirstTime && user?.vipStatus !== 'active') {
      setShowVipPrompt(true);
      return;
    }

    // 先保存到本地存储，包含翻译后的名字和类型
    localStorage.setItem('selectedReader', JSON.stringify({
      ...reader,
      name: t(`reader.${reader.id}.name`),
      type: t(`reader.${reader.id}.type`)
    }));

    try {
      // 直接导航到问题输入页面
      navigate('/ask-question');
    } catch (err) {
      // console.error('导航出错:', err);
      // 出错时也要导航到问题输入页面
      navigate('/ask-question');
    }
  };

  // 处理点赞/取消点赞
  const handleVote = async (reader: Reader, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    // 如果用户未登录，显示登录提示弹窗
    if (!user) {
      setShowLoginPrompt(true);
      return;
    }

    // 如果该占卜师正在进行投票操作，忽略本次点击
    if (isVoting[reader.id]) {
      return;
    }

    try {
      // 设置投票状态为处理中
      setIsVoting(prev => ({ ...prev, [reader.id]: true }));

      // 确保我们有准确的当前状态
      const currentVoted = reader.hasVoted || false;
      const newVoteCount = currentVoted ? (reader.voteCount || 0) - 1 : (reader.voteCount || 0) + 1;

      // 切换本地缓存状态，传入当前服务器状态
      toggleReaderVoteLocal(reader.id, currentVoted);

      // 确保结果状态是新的（与当前状态相反）
      const newVotedState = !currentVoted;

      // 更新readers数组中当前占卜师的投票状态和数量
      setReaders(prevReaders =>
        prevReaders.map(r =>
          r.id === reader.id
            ? { ...r, voteCount: newVoteCount, hasVoted: newVotedState }
            : r
        )
      );

      // 显示操作结果提示，添加根据主题适配的样式
      if (newVotedState) {
        toast.success(t('reader.vote.success'), {
          style: {
            background: isDark ? 'rgba(51, 65, 85, 0.95)' : 'rgba(255, 255, 255, 0.95)',
            color: isDark ? '#FFFFFF' : '#1F2937',
            border: isDark ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(168, 85, 247, 0.2)',
          }
        });
      } else {
        toast.success(t('reader.vote.canceled'), {
          style: {
            background: isDark ? 'rgba(51, 65, 85, 0.95)' : 'rgba(255, 255, 255, 0.95)',
            color: isDark ? '#FFFFFF' : '#1F2937',
            border: isDark ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(168, 85, 247, 0.2)',
          }
        });
      }

    } catch (error) {
      // 显示错误提示，同样添加主题适配
      toast.error(t('reader.vote.error'), {
        style: {
          background: isDark ? 'rgba(51, 65, 85, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          color: isDark ? '#FFFFFF' : '#1F2937',
          border: isDark ? '1px solid rgba(148, 163, 184, 0.2)' : '1px solid rgba(168, 85, 247, 0.2)',
        }
      });
    } finally {
      // 重置投票状态
      setIsVoting(prev => ({ ...prev, [reader.id]: false }));
    }
  };

  const getPriceLabel = (reader: Reader): PriceLabel => {
    if (reader.isFreeFirstTime) {
      return { text: t('reader.price.free_first_time', '首次免费'), className: 'bg-purple-600/70 text-white text-xs tracking-wider px-2 py-0.5 rounded' };
    }

    switch (reader.price) {
      case 'free':
        return { text: t('reader.price.free'), className: 'bg-green-500/20 text-green-400 text-xs tracking-wider px-2 py-0.5 rounded' };
      case 'paid':
        return { text: t('reader.price.vip'), className: 'bg-amber-500 font-medium text-xs tracking-wider px-2 py-0.5 rounded', style: { color: '#000' } };
      default:
        return { text: '', className: '' };
    }
  };

  const getButtonText = (reader: Reader) => {
    if (reader.isFreeFirstTime) {
      return t('reader.button.free_first_time');
    }

    if (reader.price === 'free' || user?.vipStatus === 'active') {
      return t('reader.button.select');
    }

    return t('reader.button.upgrade');
  };

  // 处理试听功能
  const handlePreviewVoice = async (reader: Reader) => {
    try {
      // 如果正在播放同一个读者的声音，则停止播放
      if (playingReaderId === reader.id) {
        if (audioElements[reader.id]) {
          audioElements[reader.id].pause();
          setPlayingReaderId(null);
        }
        return;
      }

      // 停止其他正在播放的音频
      Object.values(audioElements).forEach(audio => {
        audio.pause();
      });

      // 设置当前读者的音频为加载状态
      setLoadingAudio(prev => ({ ...prev, [reader.id]: true }));

      // 如果这个读者的音频已经加载过，直接播放
      if (audioElements[reader.id]) {
        audioElements[reader.id].currentTime = 0;

        const audio = audioElements[reader.id];

        // 设置音频结束事件
        audio.onended = () => {
          setPlayingReaderId(null);
        };

        // 尝试播放
        const playPromise = audio.play();
        if (playPromise !== undefined) {
          playPromise.then(() => {
            setPlayingReaderId(reader.id);
            setLoadingAudio(prev => ({ ...prev, [reader.id]: false }));
          }).catch(() => {
            // console.error("播放失败:", error);
            setPlayingReaderId(null);
            setLoadingAudio(prev => ({ ...prev, [reader.id]: false }));
          });
        } else {
          setPlayingReaderId(reader.id);
          setLoadingAudio(prev => ({ ...prev, [reader.id]: false }));
        }

        return;
      }

      // 获取当前语言代码 (zh, en, ja)
      const langCode = currentLanguage.split('-')[0];

      // 构建messageId，根据不同语言使用不同的格式
      // 对于中文(zh和zh-TW)使用不带语言参数的messageId，对于英文(en)和日文(ja)使用带语言参数的messageId
      const messageId = langCode === 'zh' ?
        `${reader.id}_para_0` :
        `${reader.id}_${langCode}_para_0`;

      // 从数据库tts_cache表获取音频
      const audio = await fetchParagraphAudio(
        sampleText,
        0,
        reader.id,
        'reader_intro',
        messageId, // 使用根据语言构建的messageId
        reader.id,
        `preview-${reader.id}`,
        langCode // 传递语言参数
      );

      // 设置音频结束事件
      audio.onended = () => {
        setPlayingReaderId(null);
      };

      // 保存音频元素
      setAudioElements(prev => ({
        ...prev,
        [reader.id]: audio
      }));

      // 播放音频
      const playPromise = audio.play();
      if (playPromise !== undefined) {
        playPromise.then(() => {
          setPlayingReaderId(reader.id);
          setLoadingAudio(prev => ({ ...prev, [reader.id]: false }));
        }).catch(() => {
          // console.error("播放失败:", error);
          setPlayingReaderId(null);
          setLoadingAudio(prev => ({ ...prev, [reader.id]: false }));
        });
      } else {
        setPlayingReaderId(reader.id);
        setLoadingAudio(prev => ({ ...prev, [reader.id]: false }));
      }
    } catch (error) {
      // console.error("音频处理错误:", error);
      setPlayingReaderId(null);
      setLoadingAudio(prev => ({ ...prev, [reader.id]: false }));
    }
  };



  // 添加自动刷新用户信息的 effect
  useEffect(() => {
    const refreshUserData = async () => {
      if (user) {  // 只在用户已登录时刷新
        try {
          const userData = await getCurrentUser();
          setUser(userData);
        } catch (error) {
          // 用户数据刷新失败
        }
      }
    };

    refreshUserData();
  }, []); // 组件加载时执行一次

  // 清理本地存储的 effect
  useEffect(() => {
    // 清除所有与会话相关的本地存储
    localStorage.removeItem('sessionId');
    localStorage.removeItem('userQuestion');
    localStorage.removeItem('selectedReader');
    localStorage.removeItem('selectedSpread');
    localStorage.removeItem('selectedCardsInfo');
    localStorage.removeItem('tarotDialogState');
  }, []);

  const toggleSection = (key: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  return (
    <div className="main-container min-h-screen flex flex-col relative">
      <SEO />
      <LandingBackground />
      
      {/* 主要内容 */}
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-8">
          <div className="text-center mb-4 sm:mb-6 mt-6 sm:mt-8 md:mt-10 px-2 sm:px-4 relative">
            <div className="space-y-4 sm:space-y-6">
              <h1 className={`font-bold ${i18n.language === 'en' ? 'text-3xl sm:text-4xl md:text-5xl' : 'text-4xl sm:text-5xl md:text-6xl'}`}>
                <div className="dark:text-white text-gray-900">
                  <span>{t('home.reader_selection_title.part1')}</span>
                  <span style={{color: "#C66CCD"}} className="text-purple-400 mx-2">{t('home.reader_selection_title.ai')}</span>
                  <span>{t('home.reader_selection_title.part2')}</span>
                </div>
              </h1>
              <div className="pt-1 sm:pt-2">
              <h2 className="text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto mb-0">
                {/* 桌面版本 - 中文显示一行四个，英语显示特定标签，其他语言显示两行两个 */}
                <div className="hidden sm:flex sm:flex-col sm:items-center sm:justify-center px-2">
                  <div className="flex items-center justify-center">
                    <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.reader_selection_keywords.keyword1")}</span>
                    <span className="mx-2 text-purple-500">✦</span>
                    <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.reader_selection_keywords.keyword2")}</span>
                    <span className="mx-2 text-purple-500">✦</span>
                    <span className="text-lg font-medium italic" style={{color: "#CFADF4"}}>{t("home.reader_selection_keywords.keyword3")}</span>
                  </div>
                </div>

                {/* 移动版本 - 所有语言都在一行显示3个关键词 */}
                <div className="flex sm:hidden text-center px-2 items-center justify-center">
                  <span className="text-sm font-medium italic" style={{color: "#CFADF4"}}>{t("home.reader_selection_keywords.keyword1")}</span>
                  <span className="mx-1 text-purple-500">✦</span>
                  <span className="text-sm font-medium italic" style={{color: "#CFADF4"}}>{t("home.reader_selection_keywords.keyword2")}</span>
                  <span className="mx-1 text-purple-500">✦</span>
                  <span className="text-sm font-medium italic" style={{color: "#CFADF4"}}>{t("home.reader_selection_keywords.keyword3")}</span>
                </div>
              </h2>
              </div>
            </div>
          </div>

          {/* 剩余占卜次数显示组件 */}
          <div className="text-center mb-4 sm:mb-6 mt-4 sm:mt-6">
            <RemainingReadsDisplay
              user={user}
              className="justify-center"
            />
          </div>

          {/* 占卜师选择区域 */}
          <div className="mt-0 sm:mt-8">
            {/* 占卜师卡片网格 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {readers.map((reader) => {
                const priceInfo = getPriceLabel(reader);
                const isPlaying = playingReaderId === reader.id;
                const isAudioLoading = loadingAudio[reader.id];

                return (
                  <div
                    key={reader.id}
                    className={`relative ${
                      isDark
                        ? 'bg-black/40'
                        : 'bg-[#F4F4F5]'
                    } backdrop-blur-sm rounded-xl p-6 ${
                      isDark
                        ? 'border border-purple-500/20 hover:border-purple-500/40'
                        : 'border border-purple-300/50 hover:border-purple-400/70'
                    }
                      transition-all group hover:shadow-lg ${
                        isDark ? 'hover:shadow-purple-500/10' : 'hover:shadow-purple-400/20'
                      } flex flex-col min-h-[280px] font-sans`}
                  >
                    <div className="flex items-start space-y-0">
                      <div className="relative w-24 h-24 flex-shrink-0 rounded-full overflow-hidden">
                        <CdnLazyImage
                          src={`/images-optimized/readers/${reader.nameEn}.webp`}
                          alt={t(`reader.${reader.id}.name`)}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="ml-4 flex-1">
                        <div className="flex items-center justify-between mt-2 mb-2">
                          <span className={`text-sm font-medium ${isDark ? 'text-purple-400/90' : 'text-purple-600/90'} tracking-wide uppercase font-sans`}>
                            {t(`reader.${reader.id}.type`)}
                          </span>
                          <span
                            className={`${priceInfo.className} font-sans whitespace-nowrap`}
                            style={priceInfo.style}
                          >
                            {priceInfo.text}
                          </span>
                        </div>
                        <div className="flex flex-col">
                          <div className="flex items-center gap-2">
                            <h3 className={`text-xl font-medium tracking-wide ${isDark ? 'text-white/90' : 'text-gray-800'} font-sans`}>
                              {t(`reader.${reader.id}.name`)}
                            </h3>
                            {/* 试听按钮，只在中文环境下显示 */}
                            {showPreviewButton && (
                              <button
                                onClick={(e) => {
                                  e.preventDefault();
                                  handlePreviewVoice(reader);
                                }}
                                disabled={isAudioLoading}
                                className={`px-3 py-1.5 text-sm rounded-md font-medium font-sans flex items-center gap-1.5 shadow-sm
                                  ${isDark
                                    ? isPlaying
                                      ? 'bg-purple-500/30 text-purple-200 ring-1 ring-purple-400/50 shadow-purple-500/30'
                                      : isAudioLoading
                                        ? 'bg-purple-500/20 text-purple-200/70 cursor-wait'
                                        : 'bg-purple-500/20 hover:bg-purple-500/30 text-purple-200 hover:text-purple-100 hover:shadow-purple-500/20 hover:ring-1 hover:ring-purple-400/30'
                                    : isPlaying
                                      ? 'bg-purple-200 text-purple-800 ring-1 ring-purple-300 shadow-purple-200/50'
                                      : isAudioLoading
                                        ? 'bg-purple-100 text-purple-700/70 cursor-wait'
                                        : 'bg-purple-100 hover:bg-purple-200 text-purple-700 hover:text-purple-900 hover:shadow-purple-200/50 hover:ring-1 hover:ring-purple-300'
                                  } transition-all duration-200`}
                              >
                                {isPlaying ? (
                                  <>
                                    <svg
                                      className={`w-4 h-4 ${isDark ? 'text-purple-200' : 'text-purple-700'}`}
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    >
                                      <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5" fill="currentColor" stroke="none"/>
                                      <path d="M15.54 8.46a5 5 0 0 1 0 7.07" />
                                      <path d="M19.07 4.93a10 10 0 0 1 0 14.14" />
                                    </svg>
                                    {t('reader.button.stop_preview', '停止')}
                                  </>
                                ) : isAudioLoading ? (
                                  <>
                                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-purple-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    {t('reader.button.loading')}
                                  </>
                                ) : (
                                  <>
                                    <svg
                                      className={`w-4 h-4 ${isDark ? 'text-purple-200' : 'text-purple-700'}`}
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    >
                                      <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5" fill="currentColor" stroke="none"/>
                                      <path d="M15.54 8.46a5 5 0 0 1 0 7.07" />
                                      <path d="M19.07 4.93a10 10 0 0 1 0 14.14" />
                                    </svg>
                                    {t('reader.button.preview', '试听')}
                                  </>
                                )}
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex-1 mt-4">
                      <p className={`${isDark ? 'text-gray-300' : 'text-gray-600'} text-base leading-relaxed font-sans`}>
                        {t(`reader.${reader.id}.description`)}
                      </p>
                    </div>

                    <div className="mt-6 flex items-center justify-between">
                      {/* 选择按钮 */}
                      <button
                        onClick={() => handleReaderSelect(reader)}
                        className={`flex-1 py-3 rounded-lg font-medium font-sans
                          ${reader.isFreeFirstTime
                            ? 'bg-gradient-to-r from-purple-600/90 to-indigo-600/90 hover:from-purple-500 hover:to-indigo-500 text-white shadow-md shadow-purple-500/10'
                            : reader.price === 'paid'
                              ? 'bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-400 hover:to-yellow-400 text-black shadow-lg shadow-amber-500/20'
                              : 'bg-purple-600 hover:bg-purple-500 text-white'}`}
                      >
                        {getButtonText(reader)}
                      </button>

                      {/* 点赞按钮 */}
                      <button
                        onClick={(e) => handleVote(reader, e)}
                        className={`ml-2 flex items-center justify-center w-12 h-12 rounded-full
                          ${reader.hasVoted
                            ? isDark
                              ? 'bg-red-500/90 text-white shadow-lg shadow-red-500/20'
                              : 'bg-red-500 text-white shadow-lg shadow-red-500/20'
                            : isDark
                              ? 'bg-gray-800/80 text-gray-400 hover:bg-gray-700/80 hover:text-gray-300'
                              : 'bg-gray-200 text-gray-500 hover:bg-gray-300 hover:text-gray-600'
                          } transition-all duration-200`}
                        disabled={isVoting[reader.id]}
                        aria-label={reader.hasVoted ? t('reader.vote.remove_vote') : t('reader.vote.add_vote')}
                      >
                        {isVoting[reader.id] ? (
                          <div className="w-5 h-5 border-t-2 border-r-2 border-white rounded-full animate-spin"></div>
                        ) : (
                          <>
                            <svg
                              className="w-6 h-6"
                              fill={reader.hasVoted ? "currentColor" : "none"}
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              strokeWidth={reader.hasVoted ? "0" : "2"}
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                              />
                            </svg>
                            <span className="sr-only">
                              {reader.hasVoted ? t('reader.vote.remove_vote') : t('reader.vote.add_vote')}
                            </span>
                          </>
                        )}
                      </button>

                      {/* 点赞计数 */}
                      <div className={`ml-2 text-sm ${
                        reader.hasVoted
                          ? isDark ? 'text-red-400' : 'text-red-500'
                          : isDark ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        {formatNumber(reader.voteCount || 0)}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          
          {/* 占卜注意事項區域 */}
          <div className="section-spacing max-w-[95%] lg:max-w-5xl mx-auto mt-10 sm:mt-12 lg:mt-16">
            <h2 className="main-title mb-6 dark:text-white text-gray-900">
              {t('precautions.title')}
            </h2>
            <div className="relative backdrop-blur-xl p-6 sm:p-8 rounded-2xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20">
              <div className="relative z-10">
                <div className="space-y-4">
                  {['mindset', 'question_skills', 'usage_guide', 'result_cognition', 'environment', 'special_notes', 'learning_tips'].map((section) => {
                    const items = t(`precautions.sections.${section}.items`, { returnObjects: true }) as string[];
                    return (
                      <div key={section} className="border-b dark:border-gray-700/30 border-gray-300/50 pb-4">
                        <button
                          onClick={() => toggleSection(section)}
                          className="w-full flex items-center justify-between text-left"
                        >
                          <div className="flex items-center">
                            <span className="dark:text-purple-400 text-purple-600 text-xl font-light mr-3">
                              {expandedSections[section] ? '−' : '+'}
                            </span>
                            <span className="section-title dark:text-white text-gray-800">
                              {t(`precautions.sections.${section}.title`)}
                            </span>
                          </div>
                          <span className={`transform transition-transform duration-200 ${expandedSections[section] ? 'rotate-180' : ''}`}>
                            <svg className="w-5 h-5 dark:text-gray-400 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                            </svg>
                          </span>
                        </button>
                        {expandedSections[section] && (
                          <div className="mt-4 dark:text-gray-300 text-gray-600 space-y-2 pl-6">
                            {items.map((item, index) => (
                              <div key={index} className="flex items-start space-x-2">
                                <span className="dark:text-purple-400 text-purple-600 text-sm mt-1.5">•</span>
                                <span className="body-text flex-1">
                                  {item}
                                </span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* 熱門占卜類型區域 */}
          <div className="section-spacing max-w-[95%] lg:max-w-5xl mx-auto mt-10 sm:mt-12 lg:mt-16">
            <h2 className="main-title mb-6 dark:text-white text-gray-900">
              {t('home.popular_reading_types.title')}
            </h2>
            
            <div className="space-y-6">
              {/* 愛情塔羅占卜 - 左圖右文 */}
              <div 
                className="relative backdrop-blur-xl rounded-2xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 overflow-hidden cursor-pointer"
                onClick={() => navigate('/spreads/future-lover')}
              >
                <div className="flex flex-col md:flex-row">
                  <div className="md:w-1/3 lg:w-1/4 relative">
                    <div className="aspect-square bg-gradient-to-br from-pink-400 to-purple-600 flex items-center justify-center relative overflow-hidden">
                      <CdnLazyImage 
                        src="/images-optimized/home/<USER>"
                        alt={t('home.popular_reading_types.love_tarot.title')} 
                        className="w-full h-full object-cover absolute inset-0"
                      />
                    </div>
                  </div>
                  <div className="md:w-2/3 lg:w-3/4 p-5 sm:p-6">
                    <h3 className="text-xl sm:text-2xl font-medium mb-2 dark:text-white text-gray-800">{t('home.popular_reading_types.love_tarot.title')}</h3>
                    <p className="body-text text-base sm:text-lg dark:text-gray-300 text-gray-600 mb-4">
                      {t('home.popular_reading_types.love_tarot.description')}
                    </p>
                    <div className="mb-3">
                      
                      <div className="flex flex-wrap gap-2">
                        {(t('home.popular_reading_types.love_tarot.tags', { returnObjects: true }) as string[]).map((tag) => (
                          <span key={tag} className="px-2 py-1 text-xs sm:text-sm rounded-md dark:bg-purple-900/30 bg-purple-100 dark:text-purple-300 text-purple-600">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm sm:text-base font-medium dark:text-purple-300 text-purple-600 mb-1">{t('home.suitable_questions')}：</div>
                      <p className="body-text dark:text-gray-300 text-gray-600 text-sm sm:text-base">
                        {t('home.popular_reading_types.love_tarot.suitable_questions')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 事業財運占卜 - 左文右圖 */}
              <div 
                className="relative backdrop-blur-xl rounded-2xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 overflow-hidden cursor-pointer"
                onClick={() => navigate('/spreads/career-pyramid')}
              >
                <div className="flex flex-col md:flex-row-reverse">
                  <div className="md:w-1/3 lg:w-1/4 relative">
                    <div className="aspect-square bg-gradient-to-br from-yellow-400 to-amber-600 flex items-center justify-center relative overflow-hidden">
                      <CdnLazyImage 
                        src="/images-optimized/home/<USER>" 
                        alt={t('home.popular_reading_types.career_finance.title')} 
                        className="w-full h-full object-cover absolute inset-0"
                      />
                    </div>
                  </div>
                  <div className="md:w-2/3 lg:w-3/4 p-5 sm:p-6">
                    <h3 className="text-xl sm:text-2xl font-medium mb-2 dark:text-white text-gray-800">{t('home.popular_reading_types.career_finance.title')}</h3>
                    <p className="body-text text-base sm:text-lg dark:text-gray-300 text-gray-600 mb-4">
                      {t('home.popular_reading_types.career_finance.description')}
                    </p>
                    <div className="mb-3">
                      
                      <div className="flex flex-wrap gap-2">
                        {(t('home.popular_reading_types.career_finance.tags', { returnObjects: true }) as string[]).map((tag) => (
                          <span key={tag} className="px-2 py-1 text-xs sm:text-sm rounded-md dark:bg-purple-900/30 bg-purple-100 dark:text-purple-300 text-purple-600">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm sm:text-base font-medium dark:text-purple-300 text-purple-600 mb-1">{t('home.suitable_questions')}：</div>
                      <p className="body-text dark:text-gray-300 text-gray-600 text-sm sm:text-base">
                        {t('home.popular_reading_types.career_finance.suitable_questions')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 人際關係塔羅 - 左圖右文 */}
              <div 
                className="relative backdrop-blur-xl rounded-2xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 overflow-hidden cursor-pointer"
                onClick={() => navigate('/spreads/inspiration-corr')}
              >
                <div className="flex flex-col md:flex-row">
                  <div className="md:w-1/3 lg:w-1/4 relative">
                    <div className="aspect-square bg-gradient-to-br from-blue-400 to-indigo-600 flex items-center justify-center relative overflow-hidden">
                      <CdnLazyImage 
                        src="/images-optimized/home/<USER>" 
                        alt={t('home.popular_reading_types.relationship.title')} 
                        className="w-full h-full object-cover absolute inset-0"
                      />
                    </div>
                  </div>
                  <div className="md:w-2/3 lg:w-3/4 p-5 sm:p-6">
                    <h3 className="text-xl sm:text-2xl font-medium mb-2 dark:text-white text-gray-800">{t('home.popular_reading_types.relationship.title')}</h3>
                    <p className="body-text text-base sm:text-lg dark:text-gray-300 text-gray-600 mb-4">
                      {t('home.popular_reading_types.relationship.description')}
                    </p>
                    <div className="mb-3">
                      
                      <div className="flex flex-wrap gap-2">
                        {(t('home.popular_reading_types.relationship.tags', { returnObjects: true }) as string[]).map((tag) => (
                          <span key={tag} className="px-2 py-1 text-xs sm:text-sm rounded-md dark:bg-purple-900/30 bg-purple-100 dark:text-purple-300 text-purple-600">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm sm:text-base font-medium dark:text-purple-300 text-purple-600 mb-1">{t('home.suitable_questions')}：</div>
                      <p className="body-text dark:text-gray-300 text-gray-600 text-sm sm:text-base">
                        {t('home.popular_reading_types.relationship.suitable_questions')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 人生決策指引 - 左文右圖 */}
              <div 
                className="relative backdrop-blur-xl rounded-2xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 overflow-hidden cursor-pointer"
                onClick={() => navigate('/spreads/future-development')}
              >
                <div className="flex flex-col md:flex-row-reverse">
                  <div className="md:w-1/3 lg:w-1/4 relative">
                    <div className="aspect-square bg-gradient-to-br from-green-400 to-teal-600 flex items-center justify-center relative overflow-hidden">
                      <CdnLazyImage 
                        src="/images-optimized/home/<USER>" 
                        alt={t('home.popular_reading_types.life_decision.title')} 
                        className="w-full h-full object-cover absolute inset-0"
                      />
                    </div>
                  </div>
                  <div className="md:w-2/3 lg:w-3/4 p-5 sm:p-6">
                    <h3 className="text-xl sm:text-2xl font-medium mb-2 dark:text-white text-gray-800">{t('home.popular_reading_types.life_decision.title')}</h3>
                    <p className="body-text text-base sm:text-lg dark:text-gray-300 text-gray-600 mb-4">
                      {t('home.popular_reading_types.life_decision.description')}
                    </p>
                    <div className="mb-3">
                      
                      <div className="flex flex-wrap gap-2">
                        {(t('home.popular_reading_types.life_decision.tags', { returnObjects: true }) as string[]).map((tag) => (
                          <span key={tag} className="px-2 py-1 text-xs sm:text-sm rounded-md dark:bg-purple-900/30 bg-purple-100 dark:text-purple-300 text-purple-600">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm sm:text-base font-medium dark:text-purple-300 text-purple-600 mb-1">{t('home.suitable_questions')}：</div>
                      <p className="body-text dark:text-gray-300 text-gray-600 text-sm sm:text-base">
                        {t('home.popular_reading_types.life_decision.suitable_questions')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>


          {/* 五步驟開啟您的神秘占卜之旅板块 */}
          <div className="section-spacing max-w-[95%] lg:max-w-5xl mx-auto mt-10 sm:mt-12 lg:mt-16">
            <h2 className="main-title mb-6 dark:text-white text-gray-900">
              {t('home.five_steps_title')}
            </h2>
            
            {/* 桌面端：左侧文案右侧图片的布局 */}
            <div className="hidden md:flex flex-row gap-12 items-center">
              {/* 左侧文案区域 */}
              <div className="w-1/2">
                <div className="space-y-3 relative">
                  {/* 添加移动的光标指示器 */}
                  <div 
                    className="absolute left-0 w-1 bg-purple-500 transition-all duration-300 ease-in-out"
                    style={{
                      top: `${stepCursorPosition.top}px`,
                      height: `${stepCursorPosition.height}px`,
                    }}
                  ></div>
                  
                  {/* 第一步 */}
                  <div 
                    ref={el => stepRefs.current[0] = el}
                    className={`p-3 cursor-pointer transition-all duration-300 pl-8 ${
                      selectedStep === 'step1' 
                        ? `dark:text-purple-300 text-purple-700`
                        : `dark:hover:text-purple-300 hover:text-purple-700`
                    }`}
                    onClick={() => {
                      setSelectedStep('step1');
                    }}
                  >
                    <div>
                      <h3 className={`text-lg font-semibold mb-1 ${
                        selectedStep === 'step1' ? 'dark:text-purple-300 text-purple-700' : 'dark:text-white text-gray-800'
                      }`}>{t('home.five_steps.step1.title')}</h3>
                      <p className="body-text dark:text-gray-300 text-gray-600">
                        {t('home.five_steps.step1.description')}
                      </p>
                    </div>
                  </div>

                  {/* 第二步 */}
                  <div 
                    ref={el => stepRefs.current[1] = el}
                    className={`p-3 cursor-pointer transition-all duration-300 pl-8 ${
                      selectedStep === 'step2' 
                        ? `dark:text-purple-300 text-purple-700`
                        : `dark:hover:text-purple-300 hover:text-purple-700`
                    }`}
                    onClick={() => {
                      setSelectedStep('step2');
                    }}
                  >
                    <div>
                      <h3 className={`text-lg font-semibold mb-1 ${
                        selectedStep === 'step2' ? 'dark:text-purple-300 text-purple-700' : 'dark:text-white text-gray-800'
                      }`}>{t('home.five_steps.step2.title')}</h3>
                      <p className="body-text dark:text-gray-300 text-gray-600">
                        {t('home.five_steps.step2.description')}
                      </p>
                    </div>
                  </div>

                  {/* 第三步 */}
                  <div 
                    ref={el => stepRefs.current[2] = el}
                    className={`p-3 cursor-pointer transition-all duration-300 pl-8 ${
                      selectedStep === 'step3' 
                        ? `dark:text-purple-300 text-purple-700`
                        : `dark:hover:text-purple-300 hover:text-purple-700`
                    }`}
                    onClick={() => {
                      setSelectedStep('step3');
                    }}
                  >
                    <div>
                      <h3 className={`text-lg font-semibold mb-1 ${
                        selectedStep === 'step3' ? 'dark:text-purple-300 text-purple-700' : 'dark:text-white text-gray-800'
                      }`}>{t('home.five_steps.step3.title')}</h3>
                      <p className="body-text dark:text-gray-300 text-gray-600">
                        {t('home.five_steps.step3.description')}
                      </p>
                    </div>
                  </div>

                  {/* 第四步 */}
                  <div 
                    ref={el => stepRefs.current[3] = el}
                    className={`p-3 cursor-pointer transition-all duration-300 pl-8 ${
                      selectedStep === 'step4' 
                        ? `dark:text-purple-300 text-purple-700`
                        : `dark:hover:text-purple-300 hover:text-purple-700`
                    }`}
                    onClick={() => {
                      setSelectedStep('step4');
                    }}
                  >
                    <div>
                      <h3 className={`text-lg font-semibold mb-1 ${
                        selectedStep === 'step4' ? 'dark:text-purple-300 text-purple-700' : 'dark:text-white text-gray-800'
                      }`}>{t('home.five_steps.step4.title')}</h3>
                      <p className="body-text dark:text-gray-300 text-gray-600">
                        {t('home.five_steps.step4.description')}
                      </p>
                    </div>
                  </div>

                  {/* 第五步 */}
                  <div 
                    ref={el => stepRefs.current[4] = el}
                    className={`p-3 cursor-pointer transition-all duration-300 pl-8 ${
                      selectedStep === 'step5' 
                        ? `dark:text-purple-300 text-purple-700`
                        : `dark:hover:text-purple-300 hover:text-purple-700`
                    }`}
                    onClick={() => {
                      setSelectedStep('step5');
                    }}
                  >
                    <div>
                      <h3 className={`text-lg font-semibold mb-1 ${
                        selectedStep === 'step5' ? 'dark:text-purple-300 text-purple-700' : 'dark:text-white text-gray-800'
                      }`}>{t('home.five_steps.step5.title')}</h3>
                      <p className="body-text dark:text-gray-300 text-gray-600">
                        {t('home.five_steps.step5.description')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 右侧图片区域 */}
              <div className="w-1/2 flex items-center justify-center">
                <div className="bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-900 p-1 rounded-2xl shadow-lg aspect-square w-full">
                  <div className="rounded-xl overflow-hidden relative h-full">
                    {/* 第一步图片 */}
                    <div className={`transition-opacity duration-500 ${selectedStep === 'step1' ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}>
                      <CdnLazyImage 
                        src="/images-optimized/home/<USER>"
                        alt={t('home.five_steps.step1.title')}
                        className="w-full h-full object-cover" 
                      />
                    </div>
                    
                    {/* 第二步图片 */}
                    <div className={`transition-opacity duration-500 ${selectedStep === 'step2' ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}>
                      <CdnLazyImage 
                        src="/images-optimized/home/<USER>"
                        alt={t('home.five_steps.step2.title')}
                        className="w-full h-full object-cover" 
                      />
                    </div>
                    
                    {/* 第三步图片 */}
                    <div className={`transition-opacity duration-500 ${selectedStep === 'step3' ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}>
                      <CdnLazyImage 
                        src="/images-optimized/home/<USER>"
                        alt={t('home.five_steps.step3.title')}
                        className="w-full h-full object-cover" 
                      />
                    </div>

                    {/* 第四步图片 */}
                    <div className={`transition-opacity duration-500 ${selectedStep === 'step4' ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}>
                      <CdnLazyImage 
                        src="/images-optimized/home/<USER>"
                        alt={t('home.five_steps.step4.title')}
                        className="w-full h-full object-cover" 
                      />
                    </div>

                    {/* 第五步图片 */}
                    <div className={`transition-opacity duration-500 ${selectedStep === 'step5' ? 'opacity-100' : 'opacity-0 absolute inset-0'}`}>
                      <CdnLazyImage 
                        src="/images-optimized/home/<USER>"
                        alt={t('home.five_steps.step5.title')}
                        className="w-full h-full object-cover" 
                      />
                    </div>
                    
                    {/* 添加进度指示器 */}
                    <div className="absolute bottom-3 left-0 right-0 flex justify-center space-x-2">
                      <div 
                        className={`h-1.5 w-6 rounded-full transition-all duration-300 ${selectedStep === 'step1' ? 'bg-white' : 'bg-white/30'}`}
                        onClick={() => {
                          setSelectedStep('step1');
                        }}
                        style={{ cursor: 'pointer' }}
                      ></div>
                      <div 
                        className={`h-1.5 w-6 rounded-full transition-all duration-300 ${selectedStep === 'step2' ? 'bg-white' : 'bg-white/30'}`}
                        onClick={() => {
                          setSelectedStep('step2');
                        }}
                        style={{ cursor: 'pointer' }}
                      ></div>
                      <div 
                        className={`h-1.5 w-6 rounded-full transition-all duration-300 ${selectedStep === 'step3' ? 'bg-white' : 'bg-white/30'}`}
                        onClick={() => {
                          setSelectedStep('step3');
                        }}
                        style={{ cursor: 'pointer' }}
                      ></div>
                      <div 
                        className={`h-1.5 w-6 rounded-full transition-all duration-300 ${selectedStep === 'step4' ? 'bg-white' : 'bg-white/30'}`}
                        onClick={() => {
                          setSelectedStep('step4');
                        }}
                        style={{ cursor: 'pointer' }}
                      ></div>
                      <div 
                        className={`h-1.5 w-6 rounded-full transition-all duration-300 ${selectedStep === 'step5' ? 'bg-white' : 'bg-white/30'}`}
                        onClick={() => {
                          setSelectedStep('step5');
                        }}
                        style={{ cursor: 'pointer' }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 移动端：上文下图排列 */}
            <div className="md:hidden space-y-4">
              {/* 第一步 */}
              <div className="space-y-2">
                <div className="px-4">
                  <h3 className="text-lg font-semibold mb-1 dark:text-white text-gray-800">{t('home.five_steps.step1.title')}</h3>
                  <p className="body-text dark:text-gray-300 text-gray-600">
                    {t('home.five_steps.step1.description')}
                  </p>
                </div>
                <div className="bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-900 p-1 rounded-2xl shadow-lg">
                  <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '1/1' }}>
                    <CdnLazyImage 
                      src="/images-optimized/home/<USER>"
                      alt={t('home.five_steps.step1.title')}
                      className="w-full h-full object-cover" 
                    />
                  </div>
                </div>
              </div>
              
              {/* 第二步 */}
              <div className="space-y-2">
                <div className="px-4">
                  <h3 className="text-lg font-semibold mb-1 dark:text-white text-gray-800">{t('home.five_steps.step2.title')}</h3>
                  <p className="body-text dark:text-gray-300 text-gray-600">
                    {t('home.five_steps.step2.description')}
                  </p>
                </div>
                <div className="bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-900 p-1 rounded-2xl shadow-lg">
                  <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '1/1' }}>
                    <CdnLazyImage 
                      src="/images-optimized/home/<USER>"
                      alt={t('home.five_steps.step2.title')}
                      className="w-full h-full object-cover" 
                    />
                  </div>
                </div>
              </div>
              
              {/* 第三步 */}
              <div className="space-y-2">
                <div className="px-4">
                  <h3 className="text-lg font-semibold mb-1 dark:text-white text-gray-800">{t('home.five_steps.step3.title')}</h3>
                  <p className="body-text dark:text-gray-300 text-gray-600">
                    {t('home.five_steps.step3.description')}
                  </p>
                </div>
                <div className="bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-900 p-1 rounded-2xl shadow-lg">
                  <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '1/1' }}>
                    <CdnLazyImage 
                      src="/images-optimized/home/<USER>"
                      alt={t('home.five_steps.step3.title')}
                      className="w-full h-full object-cover" 
                    />
                  </div>
                </div>
              </div>

              {/* 第四步 */}
              <div className="space-y-2">
                <div className="px-4">
                  <h3 className="text-lg font-semibold mb-1 dark:text-white text-gray-800">{t('home.five_steps.step4.title')}</h3>
                  <p className="body-text dark:text-gray-300 text-gray-600">
                    {t('home.five_steps.step4.description')}
                  </p>
                </div>
                <div className="bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-900 p-1 rounded-2xl shadow-lg">
                  <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '1/1' }}>
                    <CdnLazyImage 
                      src="/images-optimized/home/<USER>"
                      alt={t('home.five_steps.step4.title')}
                      className="w-full h-full object-cover" 
                    />
                  </div>
                </div>
              </div>

              {/* 第五步 */}
              <div className="space-y-2">
                <div className="px-4">
                  <h3 className="text-lg font-semibold mb-1 dark:text-white text-gray-800">{t('home.five_steps.step5.title')}</h3>
                  <p className="body-text dark:text-gray-300 text-gray-600">
                    {t('home.five_steps.step5.description')}
                  </p>
                </div>
                <div className="bg-gradient-to-br from-purple-100 to-pink-200 dark:from-purple-900 dark:to-pink-900 p-1 rounded-2xl shadow-lg">
                  <div className="rounded-xl overflow-hidden" style={{ aspectRatio: '1/1' }}>
                    <CdnLazyImage 
                      src="/images-optimized/home/<USER>"
                      alt={t('home.five_steps.step5.title')}
                      className="w-full h-full object-cover" 
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 為什麼選擇我們的AI塔羅占卜？板块 */}
          <div className="section-spacing max-w-[95%] lg:max-w-5xl mx-auto mt-10 sm:mt-12 lg:mt-16">
            <h2 className="main-title mb-6 dark:text-white text-gray-900">
              {t('home.why_choose_us_title')}
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {/* AI超準預測 */}
              <div className="relative backdrop-blur-xl p-4 sm:p-5 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                <div>
                  <div className="flex items-center mb-2">
                    <div className="w-10 h-10 shrink-0 rounded-full bg-purple-600/20 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium dark:text-white text-gray-800">{t('home.why_choose_us.ai_predictions.title')}</h3>
                  </div>
                  <p className="body-text dark:text-gray-300 text-gray-600">
                    {t('home.why_choose_us.ai_predictions.description')}
                  </p>
                </div>
              </div>
              
              {/* 即時回應 */}
              <div className="relative backdrop-blur-xl p-4 sm:p-5 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                <div>
                  <div className="flex items-center mb-2">
                    <div className="w-10 h-10 shrink-0 rounded-full bg-purple-600/20 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium dark:text-white text-gray-800">{t('home.why_choose_us.instant_response.title')}</h3>
                  </div>
                  <p className="body-text dark:text-gray-300 text-gray-600">
                    {t('home.why_choose_us.instant_response.description')}
                  </p>
                </div>
              </div>
              
              {/* 個性化分析 */}
              <div className="relative backdrop-blur-xl p-4 sm:p-5 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                <div>
                  <div className="flex items-center mb-2">
                    <div className="w-10 h-10 shrink-0 rounded-full bg-purple-600/20 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium dark:text-white text-gray-800">{t('home.why_choose_us.personalized_analysis.title')}</h3>
                  </div>
                  <p className="body-text dark:text-gray-300 text-gray-600">
                    {t('home.why_choose_us.personalized_analysis.description')}
                  </p>
                </div>
              </div>
              
              {/* 沉浸式抽牌 */}
              <div className="relative backdrop-blur-xl p-4 sm:p-5 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                <div>
                  <div className="flex items-center mb-2">
                    <div className="w-10 h-10 shrink-0 rounded-full bg-purple-600/20 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                        <path strokeLinecap="round" strokeLinejoin="round" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium dark:text-white text-gray-800">{t('home.why_choose_us.immersive_draw.title')}</h3>
                  </div>
                  <p className="body-text dark:text-gray-300 text-gray-600">
                    {t('home.why_choose_us.immersive_draw.description')}
                  </p>
                </div>
              </div>
              
              {/* 全方位覆蓋 */}
              <div className="relative backdrop-blur-xl p-4 sm:p-5 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300">
                <div>
                  <div className="flex items-center mb-2">
                    <div className="w-10 h-10 shrink-0 rounded-full bg-purple-600/20 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium dark:text-white text-gray-800">{t('home.why_choose_us.comprehensive_coverage.title')}</h3>
                  </div>
                  <p className="body-text dark:text-gray-300 text-gray-600">
                    {t('home.why_choose_us.comprehensive_coverage.description')}
                  </p>
                </div>
              </div>
              
              {/* 完全免費 */}
              <div className="relative backdrop-blur-xl p-4 sm:p-5 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 md:col-span-2 lg:col-span-1">
                <div>
                  <div className="flex items-center mb-2">
                    <div className="w-10 h-10 shrink-0 rounded-full bg-purple-600/20 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium dark:text-white text-gray-800">{t('home.why_choose_us.completely_free.title')}</h3>
                  </div>
                  <p className="body-text dark:text-gray-300 text-gray-600">
                    {t('home.why_choose_us.completely_free.description')}
                  </p>
                </div>
              </div>
            </div>
          </div>


          {/* 更多塔羅占卜區域 */}
          <div className="section-spacing max-w-[95%] lg:max-w-5xl mx-auto mt-10 sm:mt-12 lg:mt-16">
            <h2 className="main-title mb-6 dark:text-white text-gray-900">
              {t('home.more_tarot_options.title')}
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
              {/* 使用flex布局确保图片垂直对齐 */}
              <div 
                className="relative backdrop-blur-xl p-5 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 flex flex-col cursor-pointer"
                onClick={() => navigate('/daily-fortune')}
              >
                <div className="h-[100px]">  {/* 固定高度的文本区域 */}
                  <h3 className="font-medium text-lg sm:text-xl dark:text-white text-gray-800 mb-2">{t('home.more_tarot_options.daily_tarot.title')}</h3>
                  <p className="body-text dark:text-gray-300 text-gray-600">{t('home.more_tarot_options.daily_tarot.description')}</p>
                </div>
                <div className="mt-auto relative rounded-lg overflow-hidden flex-grow" style={{ aspectRatio: '1/1' }}>
                  <CdnLazyImage 
                    src="/images-optimized/home/<USER>" 
                    alt={t('home.more_tarot_options.daily_tarot.title')}
                    className="w-full h-full object-cover"  
                  />
                </div>
              </div>
              <div 
                className="relative backdrop-blur-xl p-5 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 flex flex-col cursor-pointer"
                onClick={() => navigate('/yes-no-tarot')}
              >
                <div className="h-[100px]">  {/* 固定高度的文本区域 */}
                  <h3 className="font-medium text-lg sm:text-xl dark:text-white text-gray-800 mb-2">{t('home.more_tarot_options.yes_no_tarot.title')}</h3>
                  <p className="body-text dark:text-gray-300 text-gray-600">{t('home.more_tarot_options.yes_no_tarot.description')}</p>
                </div>
                <div className="mt-auto relative rounded-lg overflow-hidden flex-grow" style={{ aspectRatio: '1/1' }}>
                  <CdnLazyImage 
                    src="/images-optimized/home/<USER>" 
                    alt={t('home.more_tarot_options.yes_no_tarot.title')}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <div 
                className="relative backdrop-blur-xl p-5 rounded-xl dark:bg-black/40 bg-[#F4F4F5] border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 flex flex-col cursor-pointer"
                onClick={() => navigate('/blog/complete-tarot-guide')}
              >
                <div className="h-[100px]">  {/* 固定高度的文本区域 */}
                  <h3 className="font-medium text-lg sm:text-xl dark:text-white text-gray-800 mb-2">{t('home.more_tarot_options.tarot_learning.title')}</h3>
                  <p className="body-text dark:text-gray-300 text-gray-600">{t('home.more_tarot_options.tarot_learning.description')}</p>
                </div>
                <div className="mt-auto relative rounded-lg overflow-hidden flex-grow" style={{ aspectRatio: '1/1' }}>
                  <CdnLazyImage 
                    src="/images-optimized/home/<USER>" 
                    alt={t('home.more_tarot_options.tarot_learning.title')}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* SpotlightCard组件 */}
          <Suspense
            fallback={
              <div className="w-full h-[200px] bg-gray-900 rounded-lg animate-pulse" />
            }
          >
            <div className="spotlight-section py-24 md:py-32 mt-16">
              <div className="max-w-3xl mx-auto px-2 sm:px-4">
                <SpotlightCard
                  className="custom-spotlight-card"
                  spotlightColor="rgba(0, 229, 255, 0.2)"
                >
                  <div className="p-4 sm:p-8 text-center">
                    <h3
                      className="text-2xl md:text-3xl font-semibold mb-4"
                      style={{
                        background: theme === 'light' 
                          ? "none" 
                          : "linear-gradient(135deg, #E2E8FF, #FFF1F2, #FAF5FF)",
                        WebkitBackgroundClip: theme === 'light' ? "inherit" : "text",
                        WebkitTextFillColor: theme === 'light' ? "#000" : "transparent",
                        color: theme === 'light' ? "#000" : "inherit"
                      }}
                    >
                      {t("home.explore_section.title")}
                    </h3>
                    <p className={`${
                      theme === 'light' ? 'text-gray-700' : 'text-gray-300'
                    } text-lg md:text-xl mb-6 px-1`}>
                      {t("home.explore_section.description")}
                    </p>
                    <div className="flex justify-center">
                      <motion.button
                        onClick={() => {
                          scrollToTop();
                        }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="px-6 py-3 rounded-full"
                        style={{
                          background:
                            theme === 'light'
                              ? "linear-gradient(135deg, rgba(124, 58, 237, 0.7), rgba(168, 85, 247, 0.7))"
                              : "linear-gradient(135deg, rgba(124, 58, 237, 0.8), rgba(168, 85, 247, 0.8))",
                          boxShadow: theme === 'light' 
                            ? "0 0 20px rgba(168, 85, 247, 0.4)"
                            : "0 0 20px rgba(168, 85, 247, 0.5)",
                          color: 'white',
                        }}
                      >
                        {t("home.explore_section.button")}
                      </motion.button>
                    </div>
                  </div>
                </SpotlightCard>
              </div>
            </div>
          </Suspense>


        </div>
      </div>

      <Footer />

      {/* VIP Prompt Dialog */}
      <VipPromptDialog
        isOpen={showVipPrompt}
        onCancel={() => setShowVipPrompt(false)}
      />
      
      {/* Login Prompt Dialog */}
      <LoginPrompt
        isOpen={showLoginPrompt}
        onClose={() => setShowLoginPrompt(false)}
      />
    </div>
  );
};

export default Home;