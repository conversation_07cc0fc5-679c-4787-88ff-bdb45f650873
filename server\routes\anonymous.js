const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../services/database');
const { asyncHandler } = require('../utils/asyncHandler');

/**
 * @api {post} /api/anonymous/check-eligibility 检查匿名用户是否可以使用免费占卜
 * @apiName CheckAnonymousEligibility
 * @apiGroup Anonymous
 * @apiDescription 检查浏览器指纹是否已使用过免费占卜机会
 * 
 * @apiParam {String} fingerprint 浏览器指纹
 * 
 * @apiSuccess {Boolean} canUse 是否可以使用免费占卜
 * @apiSuccess {Boolean} hasUsed 是否已使用过免费占卜
 */
router.post('/check-eligibility', asyncHandler(async (req, res) => {
  try {
    const { fingerprint } = req.body;
    
    if (!fingerprint) {
      return res.status(400).json({ error: '缺少浏览器指纹' });
    }

    const pool = await getConnection();
    
    // 检查该指纹是否已有占卜记录
    const [rows] = await pool.query(
      'SELECT COUNT(*) as count FROM anonymous_divination_records WHERE browser_fingerprint = ?',
      [fingerprint]
    );

    const hasUsed = rows[0].count > 0;
    
    // 额外的IP限制检查（可选）
    const clientIP = req.ip || req.connection.remoteAddress;
    const [ipRows] = await pool.query(
      'SELECT COUNT(*) as count FROM anonymous_divination_records WHERE ip_address = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)',
      [clientIP]
    );
    
    // 如果同一IP在1小时内已有超过3次匿名占卜，则限制使用
    const ipLimitExceeded = ipRows[0].count >= 3;
    
    res.json({
      canUse: !hasUsed && !ipLimitExceeded,
      hasUsed: hasUsed,
      ipLimitExceeded: ipLimitExceeded
    });
  } catch (error) {
    console.error('检查匿名用户资格时出错:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
}));

/**
 * @api {post} /api/anonymous/record 记录匿名占卜
 * @apiName RecordAnonymousDivination
 * @apiGroup Anonymous
 * @apiDescription 记录匿名用户的占卜信息
 * 
 * @apiParam {String} fingerprint 浏览器指纹
 * @apiParam {String} sessionId 会话ID
 * @apiParam {String} question 占卜问题
 * @apiParam {String} spreadId 牌阵ID
 * @apiParam {String} spreadName 牌阵名称
 * @apiParam {Array} selectedCards 选中的卡牌
 * @apiParam {Object} readingResult 占卜结果
 * 
 * @apiSuccess {Boolean} success 是否成功
 * @apiSuccess {String} recordId 记录ID
 */
router.post('/record', asyncHandler(async (req, res) => {
  try {
    const {
      fingerprint,
      sessionId,
      question,
      spreadId,
      spreadName,
      selectedCards,
      readingResult
    } = req.body;

    if (!fingerprint || !sessionId || !question) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    const pool = await getConnection();
    
    // 检查是否已经记录过该指纹
    const [existing] = await pool.query(
      'SELECT id FROM anonymous_divination_records WHERE browser_fingerprint = ?',
      [fingerprint]
    );

    if (existing.length > 0) {
      return res.status(400).json({ error: '该用户已使用过免费占卜机会' });
    }

    // 记录匿名占卜
    const recordId = uuidv4();
    const clientIP = req.ip || req.connection.remoteAddress;
    
    await pool.query(
      `INSERT INTO anonymous_divination_records 
       (id, browser_fingerprint, session_id, question, spread_id, spread_name, 
        selected_cards, reading_result, ip_address) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        recordId,
        fingerprint,
        sessionId,
        question,
        spreadId,
        spreadName,
        JSON.stringify(selectedCards),
        JSON.stringify(readingResult),
        clientIP
      ]
    );

    console.log('匿名占卜记录已保存:', {
      recordId,
      fingerprint: fingerprint.substring(0, 20) + '...',
      sessionId,
      question: question.substring(0, 50) + '...',
      ip: clientIP
    });

    res.json({ success: true, recordId });
  } catch (error) {
    console.error('记录匿名占卜时出错:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
}));

/**
 * @api {get} /api/anonymous/stats 获取匿名占卜统计信息（管理员用）
 * @apiName GetAnonymousStats
 * @apiGroup Anonymous
 * @apiDescription 获取匿名占卜的统计信息
 */
router.get('/stats', asyncHandler(async (req, res) => {
  try {
    const pool = await getConnection();
    
    // 获取今日匿名占卜数量
    const [todayCount] = await pool.query(
      'SELECT COUNT(*) as count FROM anonymous_divination_records WHERE DATE(created_at) = CURDATE()'
    );
    
    // 获取总匿名占卜数量
    const [totalCount] = await pool.query(
      'SELECT COUNT(*) as count FROM anonymous_divination_records'
    );
    
    // 获取最近7天的数据
    const [weeklyData] = await pool.query(`
      SELECT DATE(created_at) as date, COUNT(*) as count 
      FROM anonymous_divination_records 
      WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `);
    
    res.json({
      todayCount: todayCount[0].count,
      totalCount: totalCount[0].count,
      weeklyData: weeklyData
    });
  } catch (error) {
    console.error('获取匿名占卜统计时出错:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
}));

module.exports = router;
